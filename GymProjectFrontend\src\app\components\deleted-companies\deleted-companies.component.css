/* Deleted Companies Specific Styles */
.deleted-company-card {
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
}

.deleted-company-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Avatar Styles */
.avatar-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.avatar-circle-lg {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 18px;
}

/* Badge Styles */
.modern-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
}

.modern-badge-secondary {
  background-color: #6c757d;
  color: white;
}

.modern-badge-info {
  background-color: #0dcaf0;
  color: #000;
}

/* Animation */
.staggered-item {
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading Spinner */
.modern-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Dark Mode Support */
[data-theme="dark"] .deleted-company-card {
  background-color: var(--bg-secondary);
  border-color: var(--border-color-dark);
}

[data-theme="dark"] .deleted-company-card:hover {
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .modern-badge-info {
  background-color: #087990;
  color: white;
}

/* Responsive */
@media (max-width: 768px) {
  .avatar-circle {
    width: 35px;
    height: 35px;
    font-size: 12px;
  }
  
  .avatar-circle-lg {
    width: 50px;
    height: 50px;
    font-size: 16px;
  }
}
