/* ===== MODERN DELETED COMPANIES STYLES ===== */

/* CSS Variables */
:root {
  --deleted-primary: #e74c3c;
  --deleted-secondary: #95a5a6;
  --deleted-success: #27ae60;
  --deleted-warning: #f39c12;
  --deleted-info: #3498db;
  --deleted-light: #ecf0f1;
  --deleted-dark: #2c3e50;
  --deleted-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --deleted-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  --deleted-shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.15);
  --deleted-border-radius: 16px;
  --deleted-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== HEADER SECTION ===== */
.deleted-companies-header {
  background: var(--deleted-gradient);
  padding: 2rem 0;
  margin-bottom: 2rem;
  border-radius: 0 0 var(--deleted-border-radius) var(--deleted-border-radius);
  position: relative;
  overflow: hidden;
}

.deleted-companies-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.header-icon {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.header-icon i {
  font-size: 2.5rem;
  color: white;
}

.header-text {
  color: white;
}

.header-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header-subtitle {
  font-size: 1.1rem;
  margin: 0.5rem 0 0 0;
  opacity: 0.9;
  font-weight: 300;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.stats-card {
  background: rgba(255, 255, 255, 0.2);
  padding: 1rem 1.5rem;
  border-radius: var(--deleted-border-radius);
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.stats-number {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  line-height: 1;
}

.stats-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 0.25rem;
}

.action-buttons {
  display: flex;
  gap: 1rem;
}

.btn-action {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--deleted-border-radius);
  font-weight: 600;
  text-decoration: none;
  transition: var(--deleted-transition);
  cursor: pointer;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-action-primary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.btn-action-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.btn-action:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.3);
}

/* ===== CONTENT SECTION ===== */
.deleted-companies-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.content-wrapper {
  background: white;
  border-radius: var(--deleted-border-radius);
  box-shadow: var(--deleted-shadow);
  overflow: hidden;
  min-height: 400px;
}

/* ===== LOADING STATE ===== */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  background: white;
  border-radius: var(--deleted-border-radius);
}

.loading-content {
  text-align: center;
  padding: 3rem;
}

.loading-spinner {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto 2rem;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 4px solid transparent;
  border-radius: 50%;
  animation: spin 1.5s linear infinite;
}

.spinner-ring:nth-child(1) {
  border-top-color: var(--deleted-info);
  animation-delay: 0s;
}

.spinner-ring:nth-child(2) {
  border-right-color: var(--deleted-success);
  animation-delay: 0.3s;
}

.spinner-ring:nth-child(3) {
  border-bottom-color: var(--deleted-warning);
  animation-delay: 0.6s;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--deleted-dark);
  margin-bottom: 0.5rem;
}

.loading-subtitle {
  color: var(--deleted-secondary);
  font-size: 1rem;
}

/* ===== EMPTY STATE ===== */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  background: white;
  border-radius: var(--deleted-border-radius);
}

.empty-content {
  text-align: center;
  padding: 3rem;
  max-width: 400px;
}

.empty-icon {
  width: 120px;
  height: 120px;
  margin: 0 auto 2rem;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.empty-icon::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: inherit;
  filter: blur(20px);
  opacity: 0.3;
  z-index: -1;
}

.empty-icon i {
  font-size: 3rem;
  color: white;
}

.empty-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--deleted-dark);
  margin-bottom: 1rem;
}

.empty-subtitle {
  color: var(--deleted-secondary);
  font-size: 1.1rem;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.empty-actions {
  display: flex;
  justify-content: center;
}

/* ===== TABLE VIEW ===== */
.table-container {
  background: white;
  border-radius: var(--deleted-border-radius);
  overflow: hidden;
  box-shadow: var(--deleted-shadow);
}

.table-wrapper {
  overflow-x: auto;
}

.deleted-companies-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.deleted-companies-table thead {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.deleted-companies-table th {
  padding: 1.5rem 1rem;
  text-align: left;
  font-weight: 600;
  color: white;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: none;
}

.deleted-companies-table tbody tr {
  border-bottom: 1px solid #f8f9fa;
  transition: var(--deleted-transition);
}

.deleted-companies-table tbody tr:hover {
  background: #f8f9fa;
  transform: scale(1.01);
}

.deleted-companies-table td {
  padding: 1.5rem 1rem;
  vertical-align: middle;
  border: none;
}

.table-row {
  animation: slideInUp 0.6s ease-out both;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* User Info */
.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.user-details {
  flex: 1;
}

.user-name {
  font-weight: 600;
  color: var(--deleted-dark);
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

.user-location {
  color: var(--deleted-secondary);
  font-size: 0.85rem;
}

/* Contact Info */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.contact-item i {
  width: 16px;
  color: var(--deleted-secondary);
}

/* Company Info */
.company-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.company-name {
  font-weight: 600;
  color: var(--deleted-dark);
  font-size: 0.95rem;
}

.company-phone {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--deleted-secondary);
  font-size: 0.85rem;
}

.no-data {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--deleted-secondary);
  font-style: italic;
  font-size: 0.9rem;
}

/* Stats Info */
.stats-info {
  display: flex;
  justify-content: center;
}

.stat-item {
  text-align: center;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
  min-width: 60px;
}

.stat-number {
  font-size: 1.2rem;
  font-weight: 700;
  line-height: 1;
}

.stat-label {
  font-size: 0.75rem;
  opacity: 0.9;
  margin-top: 0.25rem;
}

/* Date Info */
.date-info {
  text-align: center;
}

.date-value {
  font-weight: 600;
  color: var(--deleted-dark);
  font-size: 0.9rem;
}

.date-label {
  color: var(--deleted-secondary);
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

/* Action Buttons */
.action-buttons-table {
  display: flex;
  justify-content: center;
}

.btn-restore {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, var(--deleted-success) 0%, #2ecc71 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--deleted-transition);
  font-size: 0.9rem;
}

.btn-restore:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(39, 174, 96, 0.3);
}

.btn-restore:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* ===== CARD VIEW ===== */
.cards-container {
  background: white;
  border-radius: var(--deleted-border-radius);
  padding: 2rem;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
}

.company-card {
  background: white;
  border-radius: var(--deleted-border-radius);
  box-shadow: var(--deleted-shadow);
  overflow: hidden;
  transition: var(--deleted-transition);
  border: 1px solid #f0f0f0;
  animation: slideInUp 0.6s ease-out both;
}

.company-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--deleted-shadow-hover);
}

.card-header-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1.5rem;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.company-avatar {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 1.5rem;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 3px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.card-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #e74c3c;
  animation: pulse 2s infinite;
}

.status-text {
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.card-content {
  padding: 2rem;
}

.company-name {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--deleted-dark);
  margin-bottom: 0.5rem;
}

.company-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--deleted-secondary);
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
}

.contact-section {
  margin-bottom: 1.5rem;
}

.contact-row {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  font-size: 0.9rem;
}

.contact-row i {
  width: 18px;
  color: var(--deleted-info);
}

.company-section {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
}

.section-title {
  font-weight: 600;
  color: var(--deleted-dark);
  margin-bottom: 0.75rem;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.company-detail {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: var(--deleted-secondary);
}

.company-detail i {
  width: 16px;
  color: var(--deleted-info);
}

.stats-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-card {
  text-align: center;
  padding: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
}

.stat-date {
  font-size: 0.9rem;
  font-weight: 600;
  line-height: 1;
}

.stat-label {
  font-size: 0.75rem;
  opacity: 0.9;
  margin-top: 0.5rem;
}

.card-footer {
  padding: 1.5rem 2rem;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.btn-restore-card {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1rem;
  background: linear-gradient(135deg, var(--deleted-success) 0%, #2ecc71 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--deleted-transition);
  font-size: 1rem;
}

.btn-restore-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(39, 174, 96, 0.3);
}

.btn-restore-card:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* ===== DARK MODE SUPPORT ===== */
[data-theme="dark"] {
  --deleted-light: #34495e;
  --deleted-dark: #ecf0f1;
  --deleted-secondary: #bdc3c7;
}

[data-theme="dark"] .content-wrapper,
[data-theme="dark"] .loading-container,
[data-theme="dark"] .empty-state,
[data-theme="dark"] .table-container,
[data-theme="dark"] .cards-container,
[data-theme="dark"] .company-card {
  background: #2c3e50;
  color: #ecf0f1;
}

[data-theme="dark"] .deleted-companies-table tbody tr:hover {
  background: #34495e;
}

[data-theme="dark"] .contact-row,
[data-theme="dark"] .company-section {
  background: #34495e;
}

[data-theme="dark"] .card-footer {
  background: #34495e;
  border-color: #4a5f7a;
}

[data-theme="dark"] .company-card {
  border-color: #4a5f7a;
}

[data-theme="dark"] .company-card:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .header-content {
    padding: 0 1.5rem;
  }

  .deleted-companies-content {
    padding: 0 1.5rem;
  }

  .cards-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .deleted-companies-header {
    padding: 1.5rem 0;
  }

  .header-content {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
    padding: 0 1rem;
  }

  .header-left {
    flex-direction: column;
    gap: 1rem;
  }

  .header-icon {
    width: 60px;
    height: 60px;
  }

  .header-icon i {
    font-size: 2rem;
  }

  .header-title {
    font-size: 2rem;
  }

  .header-actions {
    flex-direction: column;
    gap: 1rem;
    width: 100%;
  }

  .action-buttons {
    justify-content: center;
  }

  .deleted-companies-content {
    padding: 0 1rem;
  }

  .cards-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .company-card {
    margin: 0;
  }

  .card-content {
    padding: 1.5rem;
  }

  .stats-section {
    grid-template-columns: 1fr;
  }

  .table-wrapper {
    font-size: 0.85rem;
  }

  .deleted-companies-table th,
  .deleted-companies-table td {
    padding: 1rem 0.5rem;
  }

  .user-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .contact-info {
    gap: 0.25rem;
  }

  .contact-item {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .header-title {
    font-size: 1.5rem;
  }

  .header-subtitle {
    font-size: 1rem;
  }

  .btn-action {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  .company-avatar {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }

  .company-name {
    font-size: 1.2rem;
  }

  .card-header-section {
    padding: 1rem;
  }

  .card-content {
    padding: 1rem;
  }

  .card-footer {
    padding: 1rem;
  }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.company-card {
  animation: fadeInScale 0.6s ease-out both;
}

/* Stagger animation for cards */
.company-card:nth-child(1) { animation-delay: 0.1s; }
.company-card:nth-child(2) { animation-delay: 0.2s; }
.company-card:nth-child(3) { animation-delay: 0.3s; }
.company-card:nth-child(4) { animation-delay: 0.4s; }
.company-card:nth-child(5) { animation-delay: 0.5s; }
.company-card:nth-child(6) { animation-delay: 0.6s; }

/* Smooth transitions for all interactive elements */
* {
  transition: var(--deleted-transition);
}

/* Focus states for accessibility */
.btn-action:focus,
.btn-restore:focus,
.btn-restore-card:focus {
  outline: 2px solid var(--deleted-info);
  outline-offset: 2px;
}
