<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="modern-card">
        <!-- Header -->
        <div class="card-header d-flex justify-content-between align-items-center">
          <div>
            <h4 class="mb-0">
              <i class="fas fa-trash-restore me-2"></i>
              <PERSON><PERSON><PERSON>
            </h4>
            <p class="text-muted mb-0 mt-1">Silinen salon sahipleri ve salonları</p>
          </div>
          <div class="d-flex gap-2">
            <button 
              class="btn-modern btn-modern-secondary"
              (click)="toggleViewMode()"
              [title]="viewMode === 'table' ? 'Kart <PERSON>' : '<PERSON>b<PERSON>örü<PERSON>ü<PERSON>ü'"
            >
              <i [class]="viewMode === 'table' ? 'fas fa-th-large' : 'fas fa-table'"></i>
            </button>
            <button 
              class="btn-modern btn-modern-primary"
              (click)="loadDeletedCompanies()"
              title="Yenile"
            >
              <i class="fas fa-sync-alt"></i>
            </button>
          </div>
        </div>

        <!-- Content -->
        <div class="card-body">
          <!-- Loading -->
          <div *ngIf="isLoading" class="text-center py-5">
            <div class="modern-spinner"></div>
            <p class="mt-3 text-muted">Silinen salonlar yükleniyor...</p>
          </div>

          <!-- Empty State -->
          <div *ngIf="!isLoading && deletedCompanies.length === 0" class="text-center py-5">
            <i class="fas fa-trash-restore fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Silinen salon bulunamadı</h5>
            <p class="text-muted">Henüz silinmiş salon bulunmamaktadır.</p>
          </div>

          <!-- Table View -->
          <div class="table-responsive" *ngIf="!isLoading && deletedCompanies.length > 0 && viewMode === 'table'">
            <table class="modern-table">
              <thead>
                <tr>
                  <th>Salon Sahibi</th>
                  <th>Email</th>
                  <th>Telefon</th>
                  <th>Salon</th>
                  <th>Şehir</th>
                  <th>Silme Tarihi</th>
                  <th>Üye Sayısı</th>
                  <th class="text-center">İşlem</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let company of deletedCompanies" class="staggered-item">
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="avatar-circle" [style.background-color]="getAvatarColor(company.name)">
                        {{ getInitials(company.name) }}
                      </div>
                      <div class="ms-3">{{ company.name }}</div>
                    </div>
                  </td>
                  <td>{{ company.email }}</td>
                  <td>{{ company.phoneNumber }}</td>
                  <td>
                    <div *ngIf="company.companyName; else noCompany">
                      {{ company.companyName }}
                    </div>
                    <ng-template #noCompany>
                      <span class="text-muted">Atanmamış</span>
                    </ng-template>
                  </td>
                  <td>{{ company.cityName }}</td>
                  <td>{{ formatDate(company.deletedDate) }}</td>
                  <td>
                    <span class="modern-badge modern-badge-secondary">
                      {{ company.totalMembers }}
                    </span>
                  </td>
                  <td>
                    <div class="d-flex justify-content-center gap-2">
                      <button
                        class="btn-modern btn-modern-success btn-modern-icon btn-modern-icon-sm"
                        title="Geri Yükle"
                        (click)="restoreCompany(company)"
                        [disabled]="!company.canRestore"
                      >
                        <i class="fas fa-undo"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Card View -->
          <div *ngIf="!isLoading && deletedCompanies.length > 0 && viewMode === 'card'">
            <div class="row">
              <div class="col-md-4 col-lg-3 mb-4" *ngFor="let company of deletedCompanies">
                <div class="modern-card h-100 staggered-item deleted-company-card">
                  <div class="card-body text-center">
                    <div class="avatar-circle-lg mx-auto mb-3" [style.background-color]="getAvatarColor(company.name)">
                      {{ getInitials(company.name) }}
                    </div>
                    <h5 class="mb-1">{{ company.name }}</h5>
                    <p class="text-muted mb-1">{{ company.email }}</p>
                    <p class="text-muted mb-1">{{ company.phoneNumber }}</p>
                    <div class="mb-2">
                      <span class="modern-badge modern-badge-secondary">
                        {{ company.cityName }}
                      </span>
                    </div>
                    <div class="mb-2" *ngIf="company.companyName">
                      <small class="text-muted">Salon: {{ company.companyName }}</small>
                    </div>
                    <div class="mb-2">
                      <small class="text-muted">Silme: {{ formatDate(company.deletedDate) }}</small>
                    </div>
                    <div class="mb-3">
                      <span class="modern-badge modern-badge-info">
                        {{ company.totalMembers }} Üye
                      </span>
                    </div>
                    <div class="d-flex justify-content-center gap-2">
                      <button
                        class="btn-modern btn-modern-success btn-modern-icon"
                        title="Geri Yükle"
                        (click)="restoreCompany(company)"
                        [disabled]="!company.canRestore"
                      >
                        <i class="fas fa-undo"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
